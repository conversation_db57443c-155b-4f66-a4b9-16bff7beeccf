import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/notification_provider.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/common/utils/size.dart';

class NotificationTestScreen extends ConsumerWidget {
  const NotificationTestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationState = ref.watch(notificationProvider);
    final notificationNotifier = ref.read(notificationProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Test'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: widget(
        child: Padding(
          padding: EdgeInsets.all(MySize.size16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status Card
              Card(
                child: Padding(
                  padding: EdgeInsets.all(MySize.size16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification Status',
                        style: TextStyle(
                          fontSize: MySize.size18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: MySize.size12),
                      _buildStatusRow('Initialized', notificationState.isInitialized),
                      _buildStatusRow('Has Permission', notificationState.hasPermission),
                      _buildStatusRow('Loading', notificationState.isLoading),
                      if (notificationState.error != null)
                        _buildErrorRow('Error', notificationState.error!),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: MySize.size16),
              
              // FCM Token Card
              Card(
                child: Padding(
                  padding: EdgeInsets.all(MySize.size16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'FCM Token',
                        style: TextStyle(
                          fontSize: MySize.size18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: MySize.size12),
                      if (notificationState.fcmToken != null) ...[
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(MySize.size12),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(MySize.size8),
                            border: Border.all(
                              color: AppColors.primaryColor.withOpacity(0.3),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Token:',
                                style: TextStyle(
                                  fontSize: MySize.size12,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryColor,
                                ),
                              ),
                              SizedBox(height: MySize.size4),
                              Text(
                                notificationState.fcmToken!,
                                style: TextStyle(
                                  fontSize: MySize.size10,
                                  fontFamily: 'monospace',
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: MySize.size8),
                        ElevatedButton.icon(
                          onPressed: () {
                            Clipboard.setData(ClipboardData(text: notificationState.fcmToken!));
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Token copied to clipboard')),
                            );
                          },
                          icon: const Icon(Icons.copy),
                          label: const Text('Copy Token'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ] else ...[
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(MySize.size12),
                          decoration: BoxDecoration(
                            color: AppColors.error.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(MySize.size8),
                            border: Border.all(
                              color: AppColors.error.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            'No FCM token available',
                            style: TextStyle(
                              color: AppColors.error,
                              fontSize: MySize.size14,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: MySize.size16),
              
              // Action Buttons
              Card(
                child: Padding(
                  padding: EdgeInsets.all(MySize.size16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Actions',
                        style: TextStyle(
                          fontSize: MySize.size18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: MySize.size12),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: notificationState.isLoading 
                              ? null 
                              : () => notificationNotifier.initialize(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                          child: notificationState.isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text('Initialize Notifications'),
                        ),
                      ),
                      SizedBox(height: MySize.size8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => notificationNotifier.getToken(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.success,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Refresh Token'),
                        ),
                      ),
                      SizedBox(height: MySize.size8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => notificationNotifier.clearAllNotifications(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.warning,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Clear All Notifications'),
                        ),
                      ),
                      if (notificationState.error != null) ...[
                        SizedBox(height: MySize.size8),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => notificationNotifier.clearError(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.error,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Clear Error'),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: MySize.size16),
              
              // Instructions
              Card(
                child: Padding(
                  padding: EdgeInsets.all(MySize.size16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Instructions',
                        style: TextStyle(
                          fontSize: MySize.size18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: MySize.size12),
                      Text(
                        '1. Make sure notifications are initialized\n'
                        '2. Copy the FCM token\n'
                        '3. Use the token to send test notifications from Firebase Console\n'
                        '4. Check that medication reminders are working from the backend',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: MySize.size4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: MySize.size14),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size8,
              vertical: MySize.size4,
            ),
            decoration: BoxDecoration(
              color: status ? AppColors.success : AppColors.error,
              borderRadius: BorderRadius.circular(MySize.size12),
            ),
            child: Text(
              status ? 'Yes' : 'No',
              style: TextStyle(
                color: Colors.white,
                fontSize: MySize.size12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorRow(String label, String error) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: MySize.size4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.bold,
              color: AppColors.error,
            ),
          ),
          SizedBox(height: MySize.size4),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: AppColors.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(MySize.size4),
              border: Border.all(
                color: AppColors.error.withOpacity(0.3),
              ),
            ),
            child: Text(
              error,
              style: TextStyle(
                fontSize: MySize.size12,
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
