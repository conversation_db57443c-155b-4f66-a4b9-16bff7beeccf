import 'dart:developer';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:healo/models/daily_medication_model.dart';
import 'package:healo/models/medication_model.dart';
import 'package:healo/models/monthly_medication_model.dart';
import 'package:healo/models/report_model.dart';
import 'package:healo/models/report_analysis_model.dart';
import 'package:healo/models/report_health_metrics_model.dart';
import 'package:healo/models/weekly_medication_model.dart';
import 'package:intl/intl.dart';
import '../models/users_model.dart';

/// Service class to handle all Firestore-related operations
class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Returns the currently logged-in user's phone number as UID
  String? get _uid => _auth.currentUser?.phoneNumber;

  /// Updates FCM token for the current user
  Future<void> updateFCMToken(String token) async {
    if (_uid == null) return;
    try {
      await _firestore
          .collection("users")
          .doc(_uid)
          .set({'fcm_token': token}, SetOptions(merge: true));
      log("FCM token updated successfully");
    } catch (e) {
      log("Error updating FCM token: $e");
    }
  }

  /// Adds or updates user data in Firestore
  Future<void> addUser(Users user) async {
    if (_uid == null) return;
    try {
      await _firestore
          .collection("users")
          .doc(_uid)
          .set(user.toMap(), SetOptions(merge: true));
      log("User added/updated successfully");
    } catch (e) {
      log("Error adding user: $e");
    }
  }

  /// Force refresh user data from server (bypassing cache)
  Future<Users?> refreshUserFromServer() async {
    if (_uid == null) return null;
    try {
      log("Force refreshing user data from server for: $_uid");
      DocumentSnapshot<Map<String, dynamic>> doc = await _firestore
          .collection("users")
          .doc(_uid)
          .get(const GetOptions(source: Source.server));

      if (doc.exists) {
        log("User data refreshed successfully from server");
        return Users.fromMap(doc.data()!);
      }
      log("User document does not exist");
      return null;
    } catch (e) {
      log("Error refreshing user data from server: $e");
      return null;
    }
  }

  /// Fetches the user data from Firestore
  Future<Users?> getUser() async {
    if (_uid == null) return null;
    try {
      DocumentSnapshot<Map<String, dynamic>>? doc;

      // 🔄 Step 1: Try fetching from cache
      try {
        doc = await _firestore.collection("users").doc(_uid).get(
              const GetOptions(source: Source.cache),
            );
        if (doc.exists) {
          return Users.fromMap(doc.data()!);
        }
      } catch (e) {
        log("Cache miss, trying Firestore...");
      }

      // 🔄 Step 2: If cache is empty, fetch from Firestore server
      doc = await _firestore.collection("users").doc(_uid).get(
            const GetOptions(source: Source.server),
          );

      if (doc.exists) {
        return Users.fromMap(doc.data()!);
      }

      // 🔄 Step 3: If Firestore data is also not available, listen for real-time updates
      log("User data not found, switching to real-time updates...");
      try {
        return await getUserStream().first.timeout(
          const Duration(seconds: 5), // Timeout after 5 seconds
          onTimeout: () {
            log("Timeout reached, no real-time update received.");
            return null;
          },
        );
      } catch (e) {
        log("Error while waiting for real-time update: $e");
        return null;
      }
    } catch (e) {
      log("Error fetching user data: $e");
      return null;
    }
  }

  // Method to fetch real-time updates for user data using snapshots
  Stream<Users?> getUserStream() {
    String? uid = _auth.currentUser?.phoneNumber;
    if (uid == null) {
      return Stream.value(null); // Return empty stream if no user is logged in
    }

    // Listen for real-time updates
    return _firestore.collection("users").doc(uid).snapshots().map((doc) {
      if (doc.exists) {
        return Users.fromMap(doc.data() as Map<String, dynamic>);
      } else {
        return null; // Return null if user data doesn't exist
      }
    });
  }

  Future<void> addMedication(Medication medication) async {
    String? uid = _auth.currentUser?.phoneNumber;
    if (uid == null) {
      log("User not logged in");
      return;
    }

    try {
      DocumentReference userDoc = _firestore.collection("users").doc(uid);

      // Generate a unique medication ID (timestamp-based)
      String medicationId = DateTime.now().millisecondsSinceEpoch.toString();

      await userDoc.set({
        "medications": {medicationId: medication.toMap()}
      }, SetOptions(merge: true));

      log("Medication added successfully");
    } catch (e) {
      log("Error adding medication: $e");
    }
  }

  // 🔹 Fetch All Medications for the User
  Future<List<Medication>> getMedications() async {
    String? uid = _auth.currentUser?.phoneNumber;
    if (uid == null) {
      log("User not logged in");
      return [];
    }

    try {
      DocumentSnapshot userDoc =
          await _firestore.collection("users").doc(uid).get();

      if (userDoc.exists) {
        Map<String, dynamic>? data = userDoc.data() as Map<String, dynamic>?;
        if (data != null && data["medications"] != null) {
          Map<String, dynamic> medicationsData = data["medications"];
          return medicationsData.entries
              .map((entry) => Medication.fromMap(entry.key, entry.value))
              .toList();
        }
      }
    } catch (e) {
      log("Error fetching medications: $e");
    }

    return [];
  }

  // 🔹 Delete a Specific Medication
  Future<void> deleteMedication(String medicationId) async {
    String? uid = _auth.currentUser?.phoneNumber;
    if (uid == null) {
      log("User not logged in");
      return;
    }

    try {
      DocumentReference userDoc = _firestore.collection("users").doc(uid);

      await userDoc.update({
        "medications.$medicationId": FieldValue.delete(),
      });

      log("Medication deleted successfully");
    } catch (e) {
      log("Error deleting medication: $e");
    }
  }

  // 🔹 Update a Specific Medication
  Future<void> updateMedication(
      String medicationId, Medication updatedMedication) async {
    String? uid = _auth.currentUser?.phoneNumber;
    if (uid == null) {
      log("User not logged in");
      return;
    }

    try {
      DocumentReference userDoc = _firestore.collection("users").doc(uid);

      await userDoc.update({
        "medications.$medicationId": updatedMedication.toMap(),
      });

      log("Medication updated successfully");
    } catch (e) {
      log("Error updating medication: $e");
    }
  }

  // 🔹 Real-time Updates for Medications
  Stream<List<Medication>> getMedicationsStream() {
    String? uid = _auth.currentUser?.phoneNumber;
    if (uid == null) return Stream.value([]);

    return _firestore.collection("users").doc(uid).snapshots().map((doc) {
      if (doc.exists && doc.data()?["medications"] != null) {
        Map<String, dynamic> medicationsData = doc.data()?["medications"];
        return medicationsData.entries
            .map((entry) => Medication.fromMap(entry.key, entry.value))
            .toList();
      }
      return [];
    });
  }

  /// Uploads a PDF file to Firebase Storage and stores metadata in Firestore
  Future<String?> uploadPDF({required File file}) async {
    if (_uid == null) return null;
    try {
      String fileName = file.path.split('/').last;

      var existingReports = await getReports();
      if (existingReports.any((report) => report.name == fileName)) {
        log("PDF already exists");
        return null;
      }

      Reference ref = _storage.ref().child('pdfs/$_uid/$fileName');
      String downloadURL = await (await ref.putFile(file)).ref.getDownloadURL();

      // Generate a unique report ID
      final reportId = DateTime.now().millisecondsSinceEpoch.toString();

      await _firestore.collection("users").doc(_uid).update({
        "reports.$reportId": {
          "name": fileName,
          "url": downloadURL,
          "uploadedAt": Timestamp.now(),
        }
      });
      log("PDF uploaded successfully: $fileName");
      return reportId;
    } catch (e) {
      log("Error uploading PDF: $e");
      return null;
    }
  }

  /// Fetches all reports for the user
  Future<List<Report>> getReports() async {
    if (_uid == null) return [];
    try {
      var doc = await _firestore.collection("users").doc(_uid).get();
      log("Reports fetched successfully");
      return (doc.data()?['reports'] as Map<String, dynamic>?)
              ?.entries
              .map((e) => Report.fromMap(e.key, e.value))
              .toList() ??
          [];
    } catch (e) {
      log("Error fetching reports: $e");
      return [];
    }
  }

  /// Streams real-time updates for reports
  Stream<List<Report>> getReportsStream() {
    if (_uid == null) return Stream.value([]);
    return _firestore.collection("users").doc(_uid).snapshots().map((doc) {
      log("Reports stream updated");
      return (doc.data()?['reports'] as Map<String, dynamic>?)
              ?.entries
              .map((e) => Report.fromMap(e.key, e.value))
              .toList() ??
          [];
    });
  }

  /// Deletes a report from Firestore and Firebase Storage
  Future<void> deleteReport(String reportId, String reportUrl) async {
    if (_uid == null || reportId.isEmpty || reportUrl.isEmpty) return;
    try {
      await _firestore
          .collection("users")
          .doc(_uid)
          .update({"reports.$reportId": FieldValue.delete()});
      await _storage.refFromURL(reportUrl).delete();
      log("Report deleted successfully: $reportId");
    } catch (e) {
      log("Error deleting report: $e");
    }
  }

  /// Deletes a report by ID from Firestore and Firebase Storage
  Future<bool> deleteReportById(String reportId) async {
    if (_uid == null || reportId.isEmpty) return false;
    try {
      // First get the report to get the URL
      final report = await getReportById(reportId);
      if (report == null) {
        log("Report not found for deletion: $reportId");
        return false;
      }

      // Delete from Firestore
      await _firestore
          .collection("users")
          .doc(_uid)
          .update({"reports.$reportId": FieldValue.delete()});

      // Delete from Firebase Storage
      await _storage.refFromURL(report.url).delete();

      log("Report deleted successfully: $reportId");
      return true;
    } catch (e) {
      log("Error deleting report: $e");
      return false;
    }
  }

// Checks that pdf already exists or not
  Future<bool> checkIfFileExists(File file) async {
    String fileName = file.path.split('/').last;

    List<Report> reports = await getReports();

    return reports.any((report) => report.name == fileName);
  }

  /// Saves AI analysis results for a report
  Future<bool> saveReportAnalysis(String reportId, ReportAnalysis analysis) async {
    if (_uid == null || reportId.isEmpty) return false;
    try {
      await _firestore.collection("users").doc(_uid).update({
        "reports.$reportId.analysis": analysis.toMap(),
      });
      log("Analysis saved successfully for report: $reportId");
      return true;
    } catch (e) {
      log("Error saving analysis: $e");
      return false;
    }
  }

  /// Saves health metrics extracted from a report
  Future<bool> saveReportHealthMetrics(ReportHealthMetrics metrics) async {
    if (_uid == null || metrics.reportId.isEmpty) return false;

    try {
      // Save to the reports collection
      await _firestore.collection("users").doc(_uid).update({
        "reports.${metrics.reportId}.healthMetrics": metrics.toMap(),
      });

      // Also save to the appropriate health collections based on the metrics available
      await _saveMetricsToHealthCollections(metrics);

      log("Health metrics saved successfully for report: ${metrics.reportId}");
      return true;
    } catch (e) {
      log("Error saving health metrics: $e");
      return false;
    }
  }

  /// Saves metrics to the appropriate health collections
  Future<void> _saveMetricsToHealthCollections(ReportHealthMetrics metrics) async {
    if (_uid == null) return;

    final now = DateTime.now();
    final formattedDate = "${now.day}-${now.month}-${now.year}";

    // Save blood pressure data if available
    if (metrics.systolicBP != null && metrics.diastolicBP != null) {
      final bpReading = {
        'systolic': metrics.systolicBP,
        'diastolic': metrics.diastolicBP,
        'pulse': metrics.pulse,
        'day': formattedDate,
        'time': DateFormat('hh:mm a').format(now),
        'timestamp': now.millisecondsSinceEpoch,
        'source': 'report_analysis',
        'reportId': metrics.reportId,
      };

      await addBloodPressureReading(formattedDate, bpReading);
    }

    // Save blood glucose data if available
    if (metrics.fastingGlucose != null || metrics.randomGlucose != null ||
        metrics.postprandialGlucose != null || metrics.hba1c != null) {

      // Determine which glucose value to use (prioritize fasting)
      final glucoseValue = metrics.fastingGlucose ??
                          metrics.randomGlucose ??
                          metrics.postprandialGlucose;

      if (glucoseValue != null) {
        final glucoseReading = {
          'sugar': glucoseValue,
          'day': formattedDate,
          'time': DateFormat('hh:mm a').format(now),
          'timestamp': now.millisecondsSinceEpoch,
          'source': 'report_analysis',
          'reportId': metrics.reportId,
          'type': metrics.fastingGlucose != null ? 'fasting' :
                 metrics.postprandialGlucose != null ? 'postprandial' : 'random',
        };

        await addDiabetesReading(formattedDate, glucoseReading);
      }

      // Save HbA1c if available
      if (metrics.hba1c != null) {
        await addHba1cReading(formattedDate, {
          'hba1c': metrics.hba1c,
          'day': formattedDate,
          'time': DateFormat('hh:mm a').format(now),
          'timestamp': now.millisecondsSinceEpoch,
          'source': 'report_analysis',
          'reportId': metrics.reportId,
        });
      }
    }

    // Save kidney function data if available
    if (metrics.creatinine != null || metrics.bun != null ||
        metrics.gfr != null || metrics.albumin != null) {

      final kidneyReading = {
        'creatinine': metrics.creatinine,
        'bun': metrics.bun,
        'gfr': metrics.gfr,
        'albumin': metrics.albumin,
        'day': formattedDate,
        'time': DateFormat('hh:mm a').format(now),
        'timestamp': now.millisecondsSinceEpoch,
        'source': 'report_analysis',
        'reportId': metrics.reportId,
      };

      await addKidneyReading(formattedDate, kidneyReading);
    }

    // Save liver function data if available
    if (metrics.ast != null || metrics.alt != null ||
        metrics.alp != null || metrics.totalBilirubin != null) {

      final liverReading = {
        'ast': metrics.ast,
        'alt': metrics.alt,
        'alp': metrics.alp,
        'totalBilirubin': metrics.totalBilirubin,
        'day': formattedDate,
        'time': DateFormat('hh:mm a').format(now),
        'timestamp': now.millisecondsSinceEpoch,
        'source': 'report_analysis',
        'reportId': metrics.reportId,
      };

      await addLiverReading(formattedDate, liverReading);
    }

    // Save thyroid function data if available
    if (metrics.tsh != null || metrics.t3 != null || metrics.t4 != null) {
      final thyroidReading = {
        'tsh': metrics.tsh,
        't3': metrics.t3,
        't4': metrics.t4,
        'day': formattedDate,
        'time': DateFormat('hh:mm a').format(now),
        'timestamp': now.millisecondsSinceEpoch,
        'source': 'report_analysis',
        'reportId': metrics.reportId,
      };

      await addThyroidReading(formattedDate, thyroidReading);
    }

    // Save lipid profile data if available
    if (metrics.totalCholesterol != null || metrics.ldl != null ||
        metrics.hdl != null || metrics.triglycerides != null) {

      final lipidReading = {
        'totalCholesterol': metrics.totalCholesterol,
        'ldl': metrics.ldl,
        'hdl': metrics.hdl,
        'triglycerides': metrics.triglycerides,
        'day': formattedDate,
        'time': DateFormat('hh:mm a').format(now),
        'timestamp': now.millisecondsSinceEpoch,
        'source': 'report_analysis',
        'reportId': metrics.reportId,
      };

      await addLipidReading(formattedDate, lipidReading);
    }

    // Save BMI data if available
    if (metrics.weight != null && metrics.height != null) {
      // Calculate BMI if not provided
      final bmi = metrics.bmi ?? (metrics.weight! / ((metrics.height! / 100) * (metrics.height! / 100)));

      // Update BMI in the user's profile
      await updateBMI(
        bmi: bmi,
        weight: metrics.weight!,
        weightUnit: 'kg',
        height: metrics.height!,
        heightUnit: 'cm'
      );
    }

    // Save CBC data if available
    if (metrics.hemoglobin != null || metrics.wbc != null ||
        metrics.rbc != null || metrics.platelets != null) {

      final cbcReading = {
        'hemoglobin': metrics.hemoglobin,
        'wbc': metrics.wbc,
        'rbc': metrics.rbc,
        'platelets': metrics.platelets,
        'day': formattedDate,
        'time': DateFormat('hh:mm a').format(now),
        'timestamp': now.millisecondsSinceEpoch,
        'source': 'report_analysis',
        'reportId': metrics.reportId,
      };

      await addCBCReading(formattedDate, cbcReading);
    }
  }

  /// Add CBC reading to Firestore
  Future<void> addCBCReading(String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('cbc').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update CBC readings: ${e.message}");
      }
    }
  }

  /// Add lipid profile reading to Firestore
  Future<void> addLipidReading(String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('lipid_profile').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update lipid profile readings: ${e.message}");
      }
    }
  }

  /// Add liver function reading to Firestore from report analysis
  Future<void> addLiverReading(String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('liver').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update liver readings: ${e.message}");
      }
    }
  }

  /// Gets a specific report by ID
  Future<Report?> getReportById(String reportId) async {
    if (_uid == null) return null;
    try {
      var doc = await _firestore.collection("users").doc(_uid).get();
      final reportsMap = doc.data()?['reports'] as Map<String, dynamic>?;

      if (reportsMap == null || !reportsMap.containsKey(reportId)) {
        return null;
      }

      return Report.fromMap(reportId, reportsMap[reportId]);
    } catch (e) {
      log("Error fetching report: $e");
      return null;
    }
  }

//Updates water intake data into firestore
  Future<void> updateWaterIntake(int newGlasses, int todayTotal) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('water_intake').doc(_uid);

    final now = DateTime.now();
    final dateKey = "${now.day}-${now.month}-${now.year}";
    final dayName = DateFormat('EEEE').format(now);

    final docSnapshot = await docRef.get();

    Map<String, dynamic> intakeData = {};
    int currentStreak = 0;

    if (docSnapshot.exists) {
      final data = docSnapshot.data();
      intakeData = Map<String, dynamic>.from(data?['intake_data'] ?? {});
      currentStreak = data?['streak'] ?? 0;
    }

    final Map<String, dynamic> missingData = {};

    if (intakeData.isNotEmpty) {
      final existingDates = intakeData.keys.toSet();
      final parsedDates = existingDates.map((key) {
        final parts = key.split('-');
        return DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
      }).toList()
        ..sort();

      final lastDate = parsedDates.last;
      DateTime dateIterator = lastDate.add(const Duration(days: 1));

      while (dateIterator.isBefore(now)) {
        final missingDateKey =
            "${dateIterator.day}-${dateIterator.month}-${dateIterator.year}";
        missingData[missingDateKey] = {
          'day': DateFormat('EEEE').format(dateIterator),
          'glasses': 0,
          'total': todayTotal,
        };
        dateIterator = dateIterator.add(const Duration(days: 1));
      }
    }

    final todayData = {
      'day': dayName,
      'glasses': newGlasses,
      'total': todayTotal,
    };

    final updatedIntakeData = Map<String, dynamic>.from(intakeData)
      ..addAll(missingData)
      ..[dateKey] = todayData;

    final last7Days = updatedIntakeData.entries
        .where((entry) {
          final parts = entry.key.split('-');
          final date = DateTime(
              int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
          return date.isBefore(now) &&
              date.isAfter(now.subtract(const Duration(days: 7)));
        })
        .map((entry) => entry.value['glasses'] as int)
        .toList();

    final last30Days = updatedIntakeData.entries
        .where((entry) {
          final parts = entry.key.split('-');
          final date = DateTime(
              int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
          return date.isBefore(now) &&
              date.isAfter(now.subtract(const Duration(days: 30)));
        })
        .map((entry) => entry.value['glasses'] as int)
        .toList();

    final weeklyAverage = last7Days.isNotEmpty
        ? last7Days.reduce((a, b) => a + b) / last7Days.length
        : 0.0;

    final monthlyAverage = last30Days.isNotEmpty
        ? last30Days.reduce((a, b) => a + b) / last30Days.length
        : 0.0;

    final bestWeekDay =
        last7Days.isNotEmpty ? last7Days.reduce((a, b) => a > b ? a : b) : 0;

    final bestMonthDay =
        last30Days.isNotEmpty ? last30Days.reduce((a, b) => a > b ? a : b) : 0;

    final todayAlreadyExists = intakeData.containsKey(dateKey);

    if (!todayAlreadyExists) {
      final yesterday = now.subtract(const Duration(days: 1));
      final yesterdayKey =
          "${yesterday.day}-${yesterday.month}-${yesterday.year}";

      if (intakeData.containsKey(yesterdayKey)) {
        final yesterdayData = intakeData[yesterdayKey];
        if (yesterdayData['glasses'] == yesterdayData['total']) {
          currentStreak += 1;
        } else {
          currentStreak = 0;
        }
      } else {
        currentStreak = 0;
      }
    }

    // Upload updated data
    await docRef.set({
      'intake_data': updatedIntakeData,
      'weekly_average': weeklyAverage,
      'monthly_average': monthlyAverage,
      'best_week_day': bestWeekDay,
      'best_month_day': bestMonthDay,
      'streak': currentStreak,
    }, SetOptions(merge: true));
  }

//fetches Water Intake Average
  Future<double> getWaterIntakeAverage() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('water_intake').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;

      final weeklyAverage = data['weekly_average'] as double;
      return weeklyAverage;
    }

    return 0.0;
  }

//fetches Monthly Intake Average
  Future<double> getMonthlyIntakeAverage() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('water_intake').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;

      final monthlyAverage = data['monthly_average'] as double;
      return monthlyAverage;
    }

    return 0.0;
  }

//fetches Best Week Day
  Future<int> getBestWeekDay() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('water_intake').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;

      final bestWeekDay = data['best_week_day'] as int;
      return bestWeekDay;
    }

    return 0;
  }

//fetches Best Month Day
  Future<int> getBestMonthDay() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('water_intake').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;

      final bestMonthDay = data['best_month_day'] as int;
      return bestMonthDay;
    }

    return 0;
  }

//fetches Water Intake Streak
  Future<int> getStreak() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('water_intake').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;

      final streak = data['streak'] as int;
      return streak;
    }

    return 0;
  }

//fetches Water Intake Data
  Future<Map<String, dynamic>> getWaterIntakeData() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('water_intake').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;

      return data;
    }

    return {};
  }

//updates BMI
  Future<void> updateBMI({
    required double bmi,
    required double weight,
    required String weightUnit,
    required double height,
    required String heightUnit,
  }) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('bmi').doc(_uid);

    final now = DateTime.now();
    final dateKey = "${now.day}-${now.month}-${now.year}";
    final dayName = DateFormat('EEEE').format(now);
    final timeOfDay = DateFormat('h:mm a').format(now);

    final docSnapshot = await docRef.get();

    Map<String, dynamic> history = {};

    if (docSnapshot.exists) {
      history = (docSnapshot.data()?['history'] ?? {}) as Map<String, dynamic>;
    }

    final Map<String, dynamic> missingData = {};

    if (history.isNotEmpty) {
      final existingDates = history.keys.toSet();

      final parsedDates = existingDates.map((dateString) {
        final parts = dateString.split('-');
        return DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
      }).toList()
        ..sort();

      final lastDate = parsedDates.last;
      DateTime dateIterator = lastDate.add(const Duration(days: 1));

      final lastData =
          history["${lastDate.day}-${lastDate.month}-${lastDate.year}"];

      while (dateIterator.isBefore(now)) {
        final missingDateKey =
            "${dateIterator.day}-${dateIterator.month}-${dateIterator.year}";
        final missingDayName = DateFormat('EEEE').format(dateIterator);
        const missingTime = "Not Recorded";

        missingData[missingDateKey] = {
          ...lastData,
          'day': missingDayName,
          'time': missingTime,
        };

        dateIterator = dateIterator.add(const Duration(days: 1));
      }
    }
    final todayData = {
      'day': dayName,
      'time': timeOfDay,
      'bmi': bmi,
      'weight': weight,
      'weight_unit': weightUnit,
      'height': height,
      'height_unit': heightUnit,
    };

    final updatedHistory = Map<String, dynamic>.from(history)
      ..addAll(missingData)
      ..[dateKey] = todayData;

    await docRef.set({
      'history': updatedHistory,
    }, SetOptions(merge: true));
  }

//fetches BMI data from firestore
  Future<Map<String, dynamic>> fetchBMIHistory() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('bmi').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;

      return data;
    }

    return {};
  }

// Fetches the complete diabetes history from Firestore
  Future<Map<String, dynamic>> fetchDiabetesHistory() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('diabetes').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Adds a new reading to the diabetes history
  Future<void> addDiabetesReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('diabetes').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update diabetes readings: ${e.message}");
      }
    }
  }

  Future<void> addEstimatedHaba1c(double hba1c) async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('diabetes').doc(_uid);

    try {
      await docRef.update({
        'estimated_hba1c': hba1c,
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'estimated_hba1c': hba1c,
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update estimated hba1c: ${e.message}");
      }
    }
  }

  // Fetches the estimated hba1c from Firestore
  Future<double> fetchEstimatedHba1c() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('diabetes').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      final estimatedHba1c = data['estimated_hba1c'];
      log("Estimated HbA1c: ${estimatedHba1c.toString()}");
      return estimatedHba1c;
    }

    return 0.0;
  }

  // Fetches the complete hba1c history from Firestore
  Future<Map<String, dynamic>> fetchHba1cHistory() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('hba1c').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Adds a new reading to the hba1c history
  Future<void> addHba1cReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('hba1c').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update hba1c readings: ${e.message}");
      }
    }
  }

  // Add kidney reading
  Future<void> addKidneyReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('kidney').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update kidney readings: ${e.message}");
      }
    }
  }

  // Fetch the kidney history
  Future<Map<String, dynamic>> fetchKidneyHistory() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('kidney').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Add kidney symptoms
  Future<void> addKidneySymptoms(
      String date, Map<String, dynamic> symptomsData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('kidney').doc(_uid);

    try {
      await docRef.update({
        'history.$date.symptoms': FieldValue.arrayUnion([symptomsData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'symptoms': [symptomsData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update kidney symptoms: ${e.message}");
      }
    }
  }

  // Add blood pressure reading
  Future<void> addBloodPressureReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('blood_pressure').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception(
            "Failed to update blood pressure readings: ${e.message}");
      }
    }
  }

  // Fetch blood pressure history
  Future<Map<String, dynamic>> fetchBloodPressureHistory() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('blood_pressure').doc(_uid);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Add daily medication
  Future<void> addDailyMedication(DailyMedication medication) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('daily_medication').doc(_uid);

    try {
      await docRef.set({
        'medicines': FieldValue.arrayUnion([medication.toMap()]),
      }, SetOptions(merge: true));
    } on FirebaseException catch (e) {
      throw Exception("Failed to add daily medication: ${e.message}");
    }
  }

  // Fetch the daily medication history
  Future<Map<String, dynamic>> fetchDailyMedicationHistory() async {
    if (_uid == null) throw Exception("User not logged in");
    final docRef = _firestore.collection('daily_medication').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Delete daily medication
  Future<void> deleteDailyMedication(String name) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('daily_medication').doc(_uid);

    try {
      final snapshot = await docRef.get();
      final data = snapshot.data();

      if (data == null || !data.containsKey('medicines')) {
        throw Exception("No medicines found");
      }

      final List<dynamic> medicines = List.from(data['medicines']);

      final updatedMedicines = medicines.where((med) {
        return med['medicine_name'] != name;
      }).toList();

      await docRef.update({'medicines': updatedMedicines});
      log("Daily medication deleted successfully");
    } on FirebaseException catch (e) {
      throw Exception("Failed to delete daily medication: ${e.message}");
    }
  }

  // Add weekly medication
  Future<void> addWeeklyMedication(WeeklyMedication medication) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('weekly_medication').doc(_uid);

    try {
      await docRef.set({
        'medicines': FieldValue.arrayUnion([medication.toMap()]),
      }, SetOptions(merge: true));
    } on FirebaseException catch (e) {
      throw Exception("Failed to add weekly medication: ${e.message}");
    }
  }

  // Delete weekly medication
  Future<void> deleteWeeklyMedication(String name) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('weekly_medication').doc(_uid);

    try {
      final snapshot = await docRef.get();
      final data = snapshot.data();

      if (data == null || !data.containsKey('medicines')) {
        throw Exception("No medicines found");
      }

      final List<dynamic> medicines = List.from(data['medicines']);

      final updatedMedicines = medicines.where((med) {
        return med['medicine_name'] != name;
      }).toList();

      await docRef.update({'medicines': updatedMedicines});
      log("Weekly medication deleted successfully");
    } on FirebaseException catch (e) {
      throw Exception("Failed to delete weekly medication: ${e.message}");
    }
  }

  // Fetch weekly medication history
  Future<Map<String, dynamic>> fetchWeeklyMedicationHistory() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('weekly_medication').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Add monthly medication
  Future<void> addMonthlyMedication(MonthlyMedication medication) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('monthly_medication').doc(_uid);

    try {
      await docRef.set({
        'medicines': FieldValue.arrayUnion([medication.toMap()]),
      }, SetOptions(merge: true));
    } on FirebaseException catch (e) {
      throw Exception("Failed to add monthly medication: ${e.message}");
    }
  }

  // Delete monthly medication
  Future<void> deleteMonthlyMedication(String name) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('monthly_medication').doc(_uid);

    try {
      final snapshot = await docRef.get();
      final data = snapshot.data();

      if (data == null || !data.containsKey('medicines')) {
        throw Exception("No medicines found");
      }

      final List<dynamic> medicines = List.from(data['medicines']);

      final updatedMedicines = medicines.where((med) {
        return med['medicine_name'] != name;
      }).toList();

      await docRef.update({'medicines': updatedMedicines});
      log("Monthly medication deleted successfully");
    } on FirebaseException catch (e) {
      throw Exception("Failed to delete monthly medication: ${e.message}");
    }
  }

  // Fetch monthly medication history
  Future<Map<String, dynamic>> fetchMonthlyMedicationHistory() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('monthly_medication').doc(_uid);

    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Add thyroid reading
  Future<void> addThyroidReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('thyroid').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update thyroid readings: ${e.message}");
      }
    }
  }

  // Fetch thyroid history
  Future<Map<String, dynamic>> fetchThyroidHistory() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('thyroid').doc(_uid);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Add liver readings from manual entry
  Future<void> addManualLiverReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('liver').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update liver readings: ${e.message}");
      }
    }
  }

  // Fetch liver history
  Future<Map<String, dynamic>> fetchLiverHistory() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('liver').doc(_uid);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      final data = docSnapshot.data()!;
      return data;
    }

    return {};
  }

  // Add health data reading
  Future<void> addHealthDataReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('health_data').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          },
          'latest': readingData,
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update health data readings: ${e.message}");
      }
    }

    // Always update the latest data
    await docRef.update({'latest': readingData});
  }

  // Add method to fetch health history
  Future<Map<String, dynamic>> fetchHealthHistory() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('health_data').doc(_uid);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      return docSnapshot.data() ?? {};
    }

    return {};
  }

  // Add vitamin reading
  Future<void> addVitaminReading(
      String date, Map<String, dynamic> readingData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('vitamins').doc(_uid);

    try {
      await docRef.update({
        'history.$date.readings': FieldValue.arrayUnion([readingData]),
      });
    } on FirebaseException catch (e) {
      if (e.code == 'not-found') {
        await docRef.set({
          'history': {
            date: {
              'readings': [readingData],
            }
          }
        }, SetOptions(merge: true));
      } else {
        throw Exception("Failed to update vitamin readings: ${e.message}");
      }
    }
  }

  // Fetch vitamin history
  Future<Map<String, dynamic>> fetchVitaminHistory() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('vitamins').doc(_uid);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      return docSnapshot.data() ?? {};
    }

    return {};
  }

  // Fetch vitamin intake data
  Future<Map<String, dynamic>> fetchVitaminIntake() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('vitamin_intake').doc(_uid);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      return docSnapshot.data() ?? {};
    }

    return {};
  }

  // Update vitamin intake
  Future<void> updateVitaminIntake(Map<String, dynamic> intakeData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('vitamin_intake').doc(_uid);

    await docRef.set(intakeData, SetOptions(merge: true));
  }

  // Add period prediction
  Future<void> addPeriodPrediction(
      String date, Map<String, dynamic> predictionData) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);

    try {
      // Check if the document already exists
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data();
        final history = data?['history'] ?? {};

        // Overwrite the prediction for the date
        history[date] = {
          'readings': {predictionData},
        };

        await docRef.update({
          'history': history,
        });
      } else {
        // If the document doesn't exist, create it with the date and prediction
        await docRef.set({
          'history': {
            date: {
              'readings': {predictionData},
            },
          },
        });
      }
    } on FirebaseException catch (e) {
      throw Exception("Failed to save period prediction: ${e.message}");
    }
  }

  // Fetch period data
  Future<Map<String, dynamic>?> fetchPeriodData() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);
    final snapshot = await docRef.get();

    if (snapshot.exists) {
      final data = snapshot.data();
      return data;
    }
    return null;
  }

  Future<void> addSymptoms(String date, List<String> symptoms) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);

    try {
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        await docRef.update({
          'symptoms.$date': symptoms,
        });
      } else {
        await docRef.set({
          'symptoms': {
            date: symptoms,
          },
        });
      }
    } on FirebaseException catch (e) {
      throw Exception("Failed to save symptoms: ${e.message}");
    }
  }

  Future<Map<String, List<String>>?> fetchSymptoms() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);
    final snapshot = await docRef.get();

    if (snapshot.exists) {
      final data = snapshot.data();
      final symptomsMap = data?['symptoms'];

      if (symptomsMap != null && symptomsMap is Map) {
        return Map<String, List<String>>.from(
          symptomsMap.map((key, value) => MapEntry(
                key.toString(),
                List<String>.from(value),
              )),
        );
      }
    }

    return null;
  }

  Future<void> addMood(String date, String mood) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);

    try {
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        await docRef.update({
          'moods.$date': mood,
        });
      } else {
        await docRef.set({
          'moods': {
            date: mood,
          },
        });
      }
    } on FirebaseException catch (e) {
      throw Exception("Failed to save mood: ${e.message}");
    }
  }

  Future<Map<String, String>?> fetchMoods() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);
    final snapshot = await docRef.get();

    if (snapshot.exists) {
      final data = snapshot.data();
      final moodsMap = data?['moods'];

      if (moodsMap != null && moodsMap is Map) {
        return Map<String, String>.from(
          moodsMap.map((key, value) => MapEntry(
                key.toString(),
                value.toString(),
              )),
        );
      }
    }

    return null;
  }

  Future<bool> isDateInHistoryOrPhases(String dateToCheck) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);
    final snapshot = await docRef.get();

    if (!snapshot.exists) return false;

    final data = snapshot.data();
    final history = data?['history'] as Map<String, dynamic>?;

    if (history == null) return false;

    for (final entry in history.entries) {
      final readings = entry.value['readings'] as List<dynamic>?;

      if (readings == null || readings.isEmpty) {
        log("No readings found for date: $dateToCheck");
        continue;
      }

      final reading = readings.first;

      // Direct match
      if (entry.key == dateToCheck) {
        log("Direct match found");
        return true;
      }

      final phases = reading['phases'] as Map<String, dynamic>?;

      if (phases != null) {
        for (final phase in phases.entries) {
          final value = phase.value;
          if (value is Map<String, dynamic>) {
            final start = value['start'];
            final end = value['end'];
            if (start != null && end != null) {
              final checkDate = DateFormat('dd-MM-yyyy').parse(dateToCheck);
              final startDate = DateFormat('dd-MM-yyyy').parse(start);
              final endDate = DateFormat('dd-MM-yyyy').parse(end);
              if (!checkDate.isBefore(startDate) &&
                  !checkDate.isAfter(endDate)) {
                log("Date is within phase range");
                return true;
              }
            }
          } else if (phase.key == 'ovulation' &&
              value is Map<String, dynamic>) {
            if (value['day'] == dateToCheck) {
              log("Ovulation match found");
              return true;
            }
          } else if (phase.key == 'next_period' && value is String) {
            if (value == dateToCheck) {
              log("Next period match found");
              return true;
            }
          }
        }
      }
    }
    log("No match found");
    return false;
  }

  Future<void> addFlow(String date, String flow) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);

    try {
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        await docRef.update({
          'flows.$date': flow,
        });
      } else {
        await docRef.set({
          'flows': {
            date: flow,
          },
        });
      }
    } on FirebaseException catch (e) {
      throw Exception("Failed to save flow: ${e.message}");
    }
  }

  Future<void> addPre(String date, String pre) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);

    try {
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        await docRef.update({
          'pre.$date': pre,
        });
      } else {
        await docRef.set({
          'pre': {
            date: pre,
          },
        });
      }
    } on FirebaseException catch (e) {
      throw Exception("Failed to save pre: ${e.message}");
    }
  }

  Future<Map<String, String>?> fetchPre() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);
    final snapshot = await docRef.get();

    if (snapshot.exists) {
      final data = snapshot.data();
      final preMap = data?['pre'];

      if (preMap != null && preMap is Map) {
        return Map<String, String>.from(
          preMap.map((key, value) => MapEntry(
                key.toString(),
                value.toString(),
              )),
        );
      }
    }

    return null;
  }

  Future<void> addPost(String date, String post) async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);

    try {
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        await docRef.update({
          'post.$date': post,
        });
      } else {
        await docRef.set({
          'post': {
            date: post,
          },
        });
      }
    } on FirebaseException catch (e) {
      throw Exception("Failed to save post: ${e.message}");
    }
  }

  Future<Map<String, String>?> fetchPost() async {
    if (_uid == null) throw Exception("User not logged in");

    final docRef = _firestore.collection('period_data').doc(_uid);
    final snapshot = await docRef.get();

    if (snapshot.exists) {
      final data = snapshot.data();
      final postMap = data?['post'];

      if (postMap != null && postMap is Map) {
        return Map<String, String>.from(
          postMap.map((key, value) => MapEntry(
                key.toString(),
                value.toString(),
              )),
        );
      }
    }

    return null;
  }
}
