import 'dart:convert';

class Users {
  String? name;
  String? dob;
  num? weight;
  num? height;
  String? gender;
  bool initialDataCompleted;
  String? fcmToken;
  Users({
    this.name,
    this.dob,
    this.weight,
    this.height,
    this.gender,
    this.initialDataCompleted = false,
    this.fcmToken,
  });

  factory Users.fromMap(Map<String, dynamic> json) {
    return Users(
      name: json['name'],
      dob: json['dob'],
      weight: json['weight'],
      height: json['height'],
      gender: json['gender'],
      initialDataCompleted: json['initialDataCompleted'] ?? false,
    );
  }
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'dob': dob,
      'weight': weight,
      'height': height,
      'gender': gender,
      'initialDataCompleted': initialDataCompleted,
    };
  }

  String toJson() => json.encode(toMap());
  factory Users.fromJson(String source) => Users.fromMap(json.decode(source));
}
