name: healo
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  animated_splash_screen: ^1.3.0
  lottie: ^3.3.0
  pinput: ^5.0.0
  firebase_core: ^3.13.1
  firebase_auth: ^5.3.4
  fluttertoast: ^8.2.10
  flutter_svg: ^2.0.17
  url_launcher: ^6.3.1
  google_fonts: ^6.2.1
  cloud_firestore: ^5.6.2
  intl: ^0.20.2
  async_button_builder: ^3.0.0+1
  firebase_storage: ^12.4.2
  file_picker: ^8.3.5
  dio: ^5.8.0+1
  path_provider: ^2.1.5
  flutter_pdfview: ^1.4.0
  flutter_riverpod: ^2.6.1
  animated_toggle_switch: ^0.8.4
  fl_chart: ^0.70.2
  shared_preferences: ^2.5.3
  percent_indicator: ^4.2.5
  syncfusion_flutter_gauges: ^29.1.39
  syncfusion_flutter_charts: ^29.1.40
  health: ^12.2.0
  dotted_border: ^2.1.0
  firebase_ai: ^2.0.0
  flutter_markdown: ^0.7.7+1
  oauth2: ^2.0.3
  http: ^1.4.0
  url_launcher_platform_interface: ^2.3.2
  crypto: ^3.0.6
  app_links: ^6.3.2
  flutter_secure_storage: ^8.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/png/helthy_logo.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    #- assets/gif/
    #- assets/lottie/
    - assets/png/
    - assets/svg/
    - assets/svg/new_icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
